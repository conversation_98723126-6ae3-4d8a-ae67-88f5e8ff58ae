:root {
    --primary-color: #4285F4;
    --primary-dark: #3367D6;
    --secondary-color: #34A853;
    --error-color: #EA4335;
    --warning-color: #FBBC05;
    --text-color: #202124;
    --light-text: #5f6368;
    --border-color: #dadce0;
    --background-color: #f8f9fa;
    --card-background: #ffffff;
    --shadow: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--background-color);
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
}

h1 {
    color: var(--primary-color);
    margin-bottom: 10px;
}

.subtitle {
    color: var(--light-text);
    font-size: 1.1rem;
}

.form-container {
    background-color: var(--card-background);
    padding: 20px;
    border-radius: 8px;
    box-shadow: var(--shadow);
    margin-bottom: 20px;
}

.input-group {
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

input[type="text"] {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 16px;
    transition: border-color 0.3s;
}

input[type="text"]:focus {
    border-color: var(--primary-color);
    outline: none;
}

.button-group {
    display: flex;
    gap: 10px;
}

button {
    padding: 12px 20px;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.primary-btn {
    background-color: var(--primary-color);
    color: white;
}

.primary-btn:hover {
    background-color: var(--primary-dark);
}

.secondary-btn {
    background-color: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.secondary-btn:hover {
    background-color: rgba(66, 133, 244, 0.04);
}

.loading {
    display: none;
    text-align: center;
    margin: 20px 0;
}

.spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top: 4px solid var(--primary-color);
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-message {
    display: none;
    background-color: #FFEBEE;
    color: var(--error-color);
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    border-left: 4px solid var(--error-color);
}

.result-container {
    display: none;
    background-color: var(--card-background);
    padding: 20px;
    border-radius: 8px;
    box-shadow: var(--shadow);
    margin-bottom: 20px;
}

.result-container h2 {
    color: var(--primary-color);
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.info-item {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.info-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.info-label {
    font-weight: 500;
    margin-right: 10px;
    color: var(--light-text);
}

.info-value {
    font-weight: 400;
}

footer {
    text-align: center;
    margin-top: 30px;
    color: var(--light-text);
    font-size: 0.9rem;
}

footer a {
    color: var(--primary-color);
    text-decoration: none;
}

footer a:hover {
    text-decoration: underline;
}

@media (max-width: 600px) {
    .container {
        padding: 15px;
    }
    
    .button-group {
        flex-direction: column;
    }
    
    button {
        width: 100%;
    }
}
