:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  display: flex;
  min-width: 320px;
  min-height: 100vh;
}

#root {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
}

/* Dark theme styles */
.dark-theme {
  --background: #1a1a1a;
  --foreground: #ffffff;
  --card: #2a2a2a;
  --card-foreground: #ffffff;
  --border: #3a3a3a;
  --input: #2a2a2a;
  --primary: #0091ff;
  --primary-foreground: #ffffff;
  --secondary: #3a3a3a;
  --secondary-foreground: #ffffff;
  --accent: #3a3a3a;
  --accent-foreground: #ffffff;
  --destructive: #ff4d4f;
  --destructive-foreground: #ffffff;
  --muted: #3a3a3a;
  --muted-foreground: #a1a1aa;
}

/* Light theme styles */
.light-theme {
  --background: #ffffff;
  --foreground: #000000;
  --card: #ffffff;
  --card-foreground: #000000;
  --border: #e2e8f0;
  --input: #ffffff;
  --primary: #0091ff;
  --primary-foreground: #ffffff;
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;
  --accent: #f1f5f9;
  --accent-foreground: #0f172a;
  --destructive: #ff4d4f;
  --destructive-foreground: #ffffff;
  --muted: #f1f5f9;
  --muted-foreground: #64748b;
}

/* Apply theme based on system preference */
@media (prefers-color-scheme: dark) {
  :root {
    color-scheme: dark;
  }
}

/* Custom card styles */
.card {
  background-color: var(--card);
  color: var(--card-foreground);
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.card-header {
  margin-bottom: 1rem;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.card-description {
  color: var(--muted-foreground);
  font-size: 0.875rem;
}

.card-content {
  margin-bottom: 1rem;
}

.card-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

/* Form styles */
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-input {
  width: 100%;
  padding: 0.5rem;
  background-color: var(--input);
  border: 1px solid var(--border);
  border-radius: 4px;
  color: var(--foreground);
}

.form-input:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

.form-error {
  color: var(--destructive);
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

/* Button styles */
.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: background-color 0.2s, color 0.2s, border-color 0.2s;
  cursor: pointer;
}

.button-primary {
  background-color: var(--primary);
  color: var(--primary-foreground);
  border: none;
}

.button-primary:hover {
  background-color: color-mix(in srgb, var(--primary) 90%, black);
}

.button-secondary {
  background-color: var(--secondary);
  color: var(--secondary-foreground);
  border: none;
}

.button-secondary:hover {
  background-color: color-mix(in srgb, var(--secondary) 90%, black);
}

.button-outline {
  background-color: transparent;
  border: 1px solid var(--border);
  color: var(--foreground);
}

.button-outline:hover {
  background-color: var(--accent);
  color: var(--accent-foreground);
}

.button-destructive {
  background-color: var(--destructive);
  color: var(--destructive-foreground);
  border: none;
}

.button-destructive:hover {
  background-color: color-mix(in srgb, var(--destructive) 90%, black);
}

.button-icon {
  padding: 0.5rem;
}

/* Badge styles */
.badge {
  display: inline-flex;
  align-items: center;
  border-radius: 9999px;
  padding: 0.125rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.badge-primary {
  background-color: var(--primary);
  color: var(--primary-foreground);
}

.badge-secondary {
  background-color: var(--secondary);
  color: var(--secondary-foreground);
}

/* Alert styles */
.alert {
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.alert-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.alert-description {
  font-size: 0.875rem;
}

.alert-error {
  background-color: color-mix(in srgb, var(--destructive) 10%, transparent);
  border: 1px solid var(--destructive);
}

/* Separator */
.separator {
  height: 1px;
  background-color: var(--border);
  margin: 1rem 0;
}

/* Utility classes */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-4 {
  gap: 1rem;
}

.w-full {
  width: 100%;
}

.max-w-md {
  max-width: 28rem;
}

.max-w-2xl {
  max-width: 42rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.text-center {
  text-align: center;
}

.text-sm {
  font-size: 0.875rem;
}

.text-xs {
  font-size: 0.75rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-xl {
  font-size: 1.25rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.text-3xl {
  font-size: 1.875rem;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

.text-muted {
  color: var(--muted-foreground);
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
