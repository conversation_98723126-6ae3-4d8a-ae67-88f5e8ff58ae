:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Apply theme variables */
.dark-theme {
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;
}

.light-theme {
  color: #213547;
  background-color: #ffffff;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

/* Custom UI Components */
.card {
  background-color: var(--card-bg, #1a1a1a);
  border: 1px solid var(--border-color, #333);
  border-radius: 8px;
  overflow: hidden;
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color, #333);
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.card-description {
  color: var(--text-muted, rgba(255, 255, 255, 0.6));
  margin: 0;
}

.card-content {
  padding: 1.5rem;
}

.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: all 0.2s ease;
  cursor: pointer;
}

.button-primary {
  background-color: #646cff;
  color: white;
  border: none;
}

.button-primary:hover {
  background-color: #535bf2;
}

.button-outline {
  background-color: transparent;
  border: 1px solid var(--border-color, #333);
}

.button-outline:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.button-icon {
  padding: 0.5rem;
  height: 2rem;
  width: 2rem;
}

.badge {
  display: inline-flex;
  align-items: center;
  border-radius: 9999px;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.badge-primary {
  background-color: #646cff;
  color: white;
}

.badge-secondary {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-color, rgba(255, 255, 255, 0.87));
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-size: 0.875rem;
  font-weight: 500;
}

.form-input {
  padding: 0.75rem;
  border-radius: 6px;
  border: 1px solid var(--border-color, #333);
  background-color: var(--input-bg, #1a1a1a);
  color: var(--text-color, rgba(255, 255, 255, 0.87));
  font-size: 1rem;
}

.form-input:focus {
  outline: none;
  border-color: #646cff;
}

.form-error {
  color: #ff4d4f;
  font-size: 0.875rem;
}

.separator {
  height: 1px;
  background-color: var(--border-color, #333);
  margin: 1rem 0;
}

.text-muted {
  color: var(--text-muted, rgba(255, 255, 255, 0.6));
}

.alert {
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.alert-error {
  background-color: rgba(255, 77, 79, 0.1);
  border: 1px solid rgba(255, 77, 79, 0.3);
}

.alert-title {
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.alert-description {
  margin: 0;
}

/* Light theme variables */
.light-theme {
  --card-bg: #ffffff;
  --border-color: #e2e8f0;
  --text-color: #213547;
  --text-muted: rgba(0, 0, 0, 0.6);
  --input-bg: #ffffff;
}

/* Dark theme variables */
.dark-theme {
  --card-bg: #1a1a1a;
  --border-color: #333;
  --text-color: rgba(255, 255, 255, 0.87);
  --text-muted: rgba(255, 255, 255, 0.6);
  --input-bg: #1a1a1a;
}

/* Utility classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.max-w-md {
  max-width: 28rem;
}

.max-w-2xl {
  max-width: 42rem;
}

.max-w-4xl {
  max-width: 56rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.ml-1 {
  margin-left: 0.25rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.p-3 {
  padding: 0.75rem;
}

.text-center {
  text-align: center;
}

.text-xs {
  font-size: 0.75rem;
}

.text-sm {
  font-size: 0.875rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.text-3xl {
  font-size: 1.875rem;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.w-full {
  width: 100%;
}

.h-4 {
  height: 1rem;
}

.h-6 {
  height: 1.5rem;
}

.h-8 {
  height: 2rem;
}

.w-4 {
  width: 1rem;
}

.w-6 {
  width: 1.5rem;
}

.w-24 {
  width: 6rem;
}

.flex {
  display: flex;
}

.grid {
  display: grid;
}

.hidden {
  display: none;
}

.flex-1 {
  flex: 1 1 0%;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-4 {
  gap: 1rem;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.space-y-3 > * + * {
  margin-top: 0.75rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-y-8 > * + * {
  margin-top: 2rem;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.divide-y > * + * {
  border-top: 1px solid var(--border-color, #333);
}

.border-t {
  border-top: 1px solid var(--border-color, #333);
}

.border-b {
  border-bottom: 1px solid var(--border-color, #333);
}

.border-border {
  border-color: var(--border-color, #333);
}

.rounded {
  border-radius: 0.25rem;
}

.cursor-pointer {
  cursor: pointer;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.hover\:bg-muted\/50:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.hover\:text-foreground:hover {
  color: var(--text-color, rgba(255, 255, 255, 0.87));
}

.underline {
  text-decoration: underline;
}

.underline-offset-4 {
  text-underline-offset: 4px;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
