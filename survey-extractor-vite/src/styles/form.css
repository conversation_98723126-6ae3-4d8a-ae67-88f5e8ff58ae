/* Form Styles */
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-color, #e2e8f0);
  border-radius: 0.375rem;
  background-color: var(--input-bg, #ffffff);
  color: var(--text-color, #1a202c);
  font-size: 0.875rem;
  line-height: 1.5;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color, #3b82f6);
  box-shadow: 0 0 0 1px var(--primary-color, #3b82f6);
}

.form-input:disabled {
  background-color: var(--disabled-bg, #f1f5f9);
  cursor: not-allowed;
}

.form-input::placeholder {
  color: var(--placeholder-color, #94a3b8);
}

.form-error {
  color: var(--error-color, #ef4444);
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

/* Button Styles */
.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.5;
  border-radius: 0.375rem;
  transition: all 0.15s ease-in-out;
  cursor: pointer;
}

.button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.button-primary {
  background-color: var(--primary-color, #000000);
  color: white;
  border: 1px solid var(--primary-color, #000000);
}

.button-primary:hover:not(:disabled) {
  background-color: var(--primary-hover-color, #333333);
}

.button-secondary {
  background-color: transparent;
  color: var(--text-color, #1a202c);
  border: 1px solid var(--border-color, #e2e8f0);
}

.button-secondary:hover:not(:disabled) {
  background-color: var(--secondary-hover-bg, #f1f5f9);
}

/* Alert Styles */
.alert {
  padding: 1rem;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
}

.alert-error {
  background-color: var(--error-bg, #fee2e2);
  border: 1px solid var(--error-border, #fecaca);
}

.alert-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--error-color, #ef4444);
}

.alert-description {
  font-size: 0.875rem;
  color: var(--error-text, #b91c1c);
}

/* Dark Mode */
:root.dark-theme {
  --border-color: #2d3748;
  --input-bg: #1a202c;
  --text-color: #f7fafc;
  --placeholder-color: #718096;
  --disabled-bg: #2d3748;
  --primary-color: #3b82f6;
  --primary-hover-color: #2563eb;
  --secondary-hover-bg: #2d3748;
  --error-color: #f56565;
  --error-bg: #742a2a;
  --error-border: #9b2c2c;
  --error-text: #fc8181;
}
