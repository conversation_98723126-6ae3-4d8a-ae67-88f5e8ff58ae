/* Base Styles */
:root {
  --primary: #000000;
  --primary-hover: #333333;
  --background: #ffffff;
  --foreground: #1a202c;
  --muted: #64748b;
  --muted-foreground: #94a3b8;
  --border: #e2e8f0;
  --input: #ffffff;
  --ring: #3b82f6;
  --radius: 0.5rem;
  --success: #22c55e;
  --error: #ef4444;
  --card: #f8fafc;
  --card-foreground: #1a202c;
}

/* Dark theme variables */
[data-theme="dark"] {
  --background: #1a1a1a;
  --foreground: #ffffff;
  --muted: #a0aec0;
  --muted-foreground: #cbd5e1;
  --border: #333333;
  --input: #2a2a2a;
  --card: #2a2a2a;
  --card-foreground: #ffffff;
  --primary: #0091ff;
  --primary-hover: #0077cc;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: var(--background);
  color: var(--foreground);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Form Styles */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-input {
  width: 100%;
  padding: 0.75rem 0.875rem;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  background-color: var(--input);
  color: var(--foreground);
  font-size: 1rem;
  font-family: 'Inter', sans-serif;
  line-height: 1.5;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

@media (min-width: 768px) {
  .form-input {
    padding: 0.625rem 0.875rem;
    font-size: 0.875rem;
  }
}

.form-input:focus {
  outline: none;
  border-color: var(--ring);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
}

[data-theme="dark"] .form-input:focus {
  border-color: #0091ff;
  box-shadow: 0 0 0 2px rgba(0, 145, 255, 0.25);
}

.form-input:disabled {
  background-color: var(--card);
  cursor: not-allowed;
  opacity: 0.7;
}

.form-input[readonly], .readonly-input {
  background-color: #f1f5f9;
  border: 1px solid var(--border);
  color: #64748b;
  cursor: not-allowed;
  opacity: 0.9;
}

.form-input::placeholder {
  color: var(--muted-foreground);
}

.form-error {
  color: var(--error);
  font-size: 0.75rem;
  margin-top: 0.375rem;
}

/* Button Styles */
.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.25rem;
  font-size: 1rem;
  font-weight: 500;
  font-family: 'Inter', sans-serif;
  line-height: 1.5;
  border-radius: var(--radius);
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  white-space: nowrap;
}

@media (min-width: 768px) {
  .button {
    padding: 0.625rem 1.25rem;
    font-size: 0.875rem;
  }
}

.button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.button-primary {
  background-color: var(--primary);
  color: white;
  border: 1px solid var(--primary);
}

.button-primary:hover:not(:disabled) {
  background-color: var(--primary-hover);
}

.button-secondary {
  background-color: transparent;
  color: var(--foreground);
  border: 1px solid var(--border);
}

.button-secondary:hover:not(:disabled) {
  background-color: var(--card);
}

/* Alert Styles */
.alert {
  padding: 1rem;
  border-radius: var(--radius);
  margin-bottom: 1.5rem;
}

.alert-error {
  background-color: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.alert-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--error);
}

.alert-description {
  font-size: 0.875rem;
  color: var(--error);
  opacity: 0.8;
}

/* Layout Styles */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 0.75rem;
}

@media (min-width: 768px) {
  .container {
    padding: 0 1.25rem;
  }
}

.header {
  padding: 1.5rem 0;
  border-bottom: 1px solid var(--border);
  margin-bottom: 1.5rem;
}

.header h1 {
  font-size: 1.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 0.5rem;
}

.header p {
  text-align: center;
  color: var(--muted);
  font-size: 0.875rem;
}

@media (min-width: 768px) {
  .header {
    padding: 2rem 0;
    margin-bottom: 2.5rem;
  }

  .header h1 {
    font-size: 2rem;
    margin-bottom: 0.75rem;
  }

  .header p {
    font-size: 1rem;
  }
}

.footer {
  padding: 2rem 0;
  border-top: 1px solid var(--border);
  margin-top: 3rem;
  text-align: center;
  font-size: 0.875rem;
  color: var(--muted);
}

/* Multi-step Form Styles */
.multi-step-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

@media (min-width: 768px) {
  .multi-step-form {
    flex-direction: row;
    align-items: flex-start;
  }
}

.sidebar {
  background-color: var(--card);
  border-radius: var(--radius);
  padding: 1.25rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  margin-bottom: 1.5rem;
}

@media (min-width: 768px) {
  .sidebar {
    width: 18rem;
    flex-shrink: 0;
    padding: 1.5rem;
    margin-bottom: 0;
    position: sticky;
    top: 2rem;
  }
}

.form-content {
  flex: 1;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 1.25rem;
  background-color: var(--background);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

@media (min-width: 768px) {
  .form-content {
    padding: 2rem;
  }
}

.step-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border-radius: var(--radius);
  transition: background-color 0.15s ease-in-out;
  margin-bottom: 0.5rem;
}

.step-item.active {
  background-color: rgba(0, 0, 0, 0.05);
}

[data-theme="dark"] .step-item.active {
  background-color: rgba(255, 255, 255, 0.08);
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.75rem;
  height: 1.75rem;
  border-radius: 9999px;
  margin-right: 0.875rem;
  font-size: 0.75rem;
  font-weight: 600;
  background-color: var(--card);
  border: 1px solid var(--border);
  color: var(--muted);
}

.step-number.active {
  background-color: var(--primary);
  border-color: var(--primary);
  color: white;
}

.step-number.completed {
  background-color: var(--success);
  border-color: var(--success);
  color: white;
}

.step-title {
  font-size: 0.875rem;
  font-weight: 500;
}

/* Grid and Responsive Utilities */
.grid {
  display: grid;
  gap: 1.5rem;
}

.grid-cols-1 {
  grid-template-columns: 1fr;
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
  }
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-end {
  justify-content: flex-end;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mt-8 {
  margin-top: 2rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.border-t {
  border-top: 1px solid var(--border);
}

.pt-4 {
  padding-top: 1rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.text-sm {
  font-size: 0.875rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-xl {
  font-size: 1.25rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.rounded-lg {
  border-radius: var(--radius);
}

.bg-gray-50 {
  background-color: var(--card);
}

[data-theme="dark"] .bg-gray-50 {
  background-color: rgba(255, 255, 255, 0.03);
}

.text-gray-500 {
  color: var(--muted);
}

.text-gray-600 {
  color: var(--muted);
}

.text-green-600 {
  color: var(--success);
}

.w-full {
  width: 100%;
}

.w-24 {
  width: 6rem;
}

.flex-1 {
  flex: 1;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.left-3 {
  left: 0.75rem;
}

.top-1\/2 {
  top: 50%;
}

.-translate-y-1\/2 {
  transform: translateY(-50%);
}

.pl-10 {
  padding-left: 2.5rem;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.min-h-screen {
  min-height: 100vh;
}

/* Mobile Progress Bar Styles */
.mobile-progress-bar {
  display: block;
  margin: 0.5rem 0 2rem;
  padding: 0 0.5rem;
}

@media (min-width: 768px) {
  .mobile-progress-bar {
    display: none;
  }
}

.progress-steps {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 0;
}

.progress-step-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.progress-step {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.25rem;
  height: 2.25rem;
  border-radius: 9999px;
  background-color: #f1f1f1;
  border: 2px solid #e0e0e0;
  color: #9e9e9e;
  font-weight: 600;
  font-size: 0.875rem;
  z-index: 10;
  transition: all 0.2s ease;
}

[data-theme="dark"] .progress-step {
  background-color: #2a2a2a;
  border: 2px solid #333333;
  color: #a0aec0;
}

.progress-step-active {
  background-color: #0091ff;
  border-color: #0091ff;
  color: white;
  box-shadow: 0 0 0 4px rgba(0, 145, 255, 0.2);
}

.progress-step-completed {
  background-color: #22c55e;
  border-color: #22c55e;
  color: white;
}

.progress-connector {
  flex: 1;
  height: 2px;
  background-color: #e0e0e0;
  min-width: 1rem;
  max-width: 2.5rem;
  transition: background-color 0.2s ease;
}

[data-theme="dark"] .progress-connector {
  background-color: #333333;
}

.progress-connector-active {
  background-color: #0091ff;
}

.progress-step-title {
  font-size: 0.7rem;
  font-weight: 500;
  color: #9e9e9e;
  margin-top: 0.5rem;
  text-align: center;
  max-width: 4rem;
  transition: color 0.2s ease;
}

[data-theme="dark"] .progress-step-title {
  color: #a0aec0;
}

.progress-step-title-active {
  color: #0091ff;
  font-weight: 600;
}

.progress-step-title-completed {
  color: #22c55e;
}

/* Desktop Sidebar Styles (hide on mobile) */
@media (max-width: 767px) {
  .sidebar .space-y-2 {
    display: none;
  }
}
